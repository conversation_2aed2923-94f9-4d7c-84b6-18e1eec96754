"""
提供基于提示词的模型问答
模型+MCP的简单Agent文档
多agent组成的team问答
"""

import asyncio
import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

from autogen_agentchat.teams import RoundRobinGroupChat
from sqlmodel import Session, create_engine, select
from autogenstudio.datamodel.db import Team
from autogen_agentchat.messages import TextMessage
from io import BytesIO

import requests
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage
from PIL import Image
from autogen_core.models import UserMessage
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.tools.mcp import StreamableHttpServerParams,McpWorkbench
from autogen_agentchat.agents import AssistantAgent
 

def load_team_from_id(team_id: int) -> Optional[Dict[str, Any]]:
    """Load team configuration from database by team ID

    Args:
        team_id: The ID of the team to load from the database

    Returns:
        The team component configuration as a dictionary, or None if not found

    Raises:
        Exception: If database connection or query fails
    """
    # Construct database path: %Home%\.autogenstudio\autogen04203.db
    home_dir = Path.home()
    db_path = home_dir / ".autogenstudio" / "autogen04203.db"

    # Create database URI
    db_uri = f"sqlite:///{db_path}"

    # Create database engine with SQLite-specific settings
    engine = create_engine(db_uri, connect_args={"check_same_thread": False})

    try:
        with Session(engine) as session:
            # Query team by ID
            statement = select(Team).where(Team.id == team_id)
            team = session.exec(statement).first()

            if team is None:
                return None

            # Return the component field (JSON data)
            return team.component

    except Exception as e:
        raise Exception(f"Failed to load team from database: {str(e)}")
    finally:
        # Clean up engine
        engine.dispose()


async def load_team_from_file(path: Union[str, Path]) -> Dict[str, Any]:
    """Load team configuration from JSON file"""
    path = Path(path)
    if not path.exists():
        raise FileNotFoundError(f"Team config file not found: {path}")
    
    with open(path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    return config


def create_team_from_config(config: Dict[str, Any]):
    """Create team instance from configuration dictionary"""
    try:
        # Use AutoGen's component loading system
        team = RoundRobinGroupChat.load_component(config)
        return team
    except Exception as e:
        print(f"Failed to create team: {e}")
        raise


async def load_and_run_team(
    team_config_path: str, 
    task: str,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None
) -> Any:
    """Complete workflow: load team config and run task"""
    
    # Set environment variables if provided
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key
    if base_url:
        os.environ["OPENAI_BASE_URL"] = base_url
    
    # Load team configuration
    config = await load_team_from_file(team_config_path)
    print(f"Loaded team config from {team_config_path}")
    
    # Create team instance
    team = create_team_from_config(config)
    print(f"Created team instance: {type(team).__name__}")
    
    # Run the task
    print(f"Running task: {task}")
    result = await team.run(task=task)
    
    return result

async def ollama_model_test():
    # Assuming your Ollama server is running locally on port 11434.
    model_client = OllamaChatCompletionClient(model="qwen3:latest")
    response = await model_client.create([UserMessage(content="What is the capital of France?", source="user")])
    print(response)
    mcp_server = StreamableHttpServerParams(
        url="http://localhost:11435/mcp",
        headers={"Authorization": "Bearer your-api-key", "Content-Type": "application/json"},
        timeout=30,
        sse_read_timeout=60 * 5,
        terminate_on_close=True,
    )
    # Create an MCP workbench which provides a session to the mcp server.
    async with McpWorkbench(mcp_server) as workbench:  # type: ignore
        # Create an agent that can use the fetch tool.
        mcp_agent = AssistantAgent(
            name="mcp_agent", model_client=model_client, workbench=workbench, reflect_on_tool_use=True
        )

        # Let the agent fetch the content of a URL and summarize it.
        result = await mcp_agent.run(task="calculate 11937*33887")
        assert isinstance(result.messages[-1], TextMessage)
        print(result.messages[-1].content)

        # Close the connection to the model client.
        await model_client.close()
def chat_backend(Message,MCP_url_list,team_id):
    pass

async def main(stream=False):
    """Main execution function"""
    # Set API credentials
    os.environ["OPENAI_API_KEY"] = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
    os.environ["OPENAI_BASE_URL"] = "https://gnomic.nengyongai.cn/v1"
    
    try:
        # Load team configuration
        #config = await load_team_from_file("test_team.json")
        config = load_team_from_id(6)
        # Create team instance
        team = create_team_from_config(config)
        if not stream:
            # Run task
            result = await team.run(task="Write a short poem about the fall season.")
            print("Task completed successfully!")
            print("Result:", result)
        else:
            text_message0 = TextMessage(content="You are a web tool", source="System")
            text_message1 = TextMessage(content="Search Most import ten things at today", source="User")
            # pil_image = Image.open(BytesIO(requests.get("https://picsum.photos/300/200").content))
            # img = AGImage(pil_image)
            # multi_modal_message = MultiModalMessage(content=["Can you describe the content of this image?", img], source="User")

            stream = team.run_stream(task=[text_message0,text_message1])
            async for message in stream:
                if message.source not  in ["User","System"]:
                    print(message.source,":",message.content)
    except FileNotFoundError:
        print("Error: test_team.json not found. Please ensure the team config file exists.")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(ollama_model_test())
