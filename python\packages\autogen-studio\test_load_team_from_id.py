#!/usr/bin/env python3
"""
Test script for load_team_from_id function
"""

import json
from chat_backend import load_team_from_id


def test_load_team_from_id():
    """Test the load_team_from_id function"""
    
    print("Testing load_team_from_id function...")
    
    # Test with a non-existent team ID
    print("\n1. Testing with non-existent team ID (999):")
    try:
        result = load_team_from_id(999)
        if result is None:
            print("✓ Correctly returned None for non-existent team")
        else:
            print(f"✗ Expected None, got: {result}")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    # Test with team ID 1 (if it exists)
    print("\n2. Testing with team ID 1:")
    try:
        result = load_team_from_id(1)
        if result is None:
            print("✓ Team ID 1 not found (returned None)")
        else:
            print("✓ Successfully loaded team configuration:")
            print(f"   Type: {type(result)}")
            if isinstance(result, dict):
                print(f"   Keys: {list(result.keys())}")
                # Pretty print the JSON if it's not too large
                json_str = json.dumps(result, indent=2)
                if len(json_str) < 500:
                    print(f"   Content:\n{json_str}")
                else:
                    print(f"   Content: [Large JSON object - {len(json_str)} characters]")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    # Test with team ID 2 (if it exists)
    print("\n3. Testing with team ID 2:")
    try:
        result = load_team_from_id(2)
        if result is None:
            print("✓ Team ID 2 not found (returned None)")
        else:
            print("✓ Successfully loaded team configuration:")
            print(f"   Type: {type(result)}")
            if isinstance(result, dict):
                print(f"   Keys: {list(result.keys())}")
    except Exception as e:
        print(f"✗ Error: {e}")


if __name__ == "__main__":
    test_load_team_from_id()
